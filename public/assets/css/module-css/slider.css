/*--------------------------------------------------------------
# Main Slider
--------------------------------------------------------------*/
.main-slider {
  position: relative;
  background-color: var(--tecture-black);
  z-index: 10;
}

.main-slider .item {
  background-color: var(--tecture-black);
  position: relative;
  padding-top: 311px;
  padding-bottom: 181px;
  z-index: 10;
}

.main-slider__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: transform 7000ms ease, opacity 1500ms ease-in;
  transition: transform 7000ms ease, opacity 1500ms ease-in,
    -webkit-transform 7000ms ease;
  z-index: 1;
}

.main-slider__bg:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(8, 10, 7, 0.45);
  z-index: -1;
}

.swiper-slide-active .main-slider__bg {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.main-slider__shape-1 {
  position: absolute;
  top: 0;
  right: 217px;
  border-top: 250px solid var(--tecture-base);
  border-right: 215px solid transparent;
  border-left: 314px solid transparent;
  opacity: 0;
  transform: translateY(-200px);
  z-index: 1;
}

.swiper-slide-active .main-slider__shape-1 {
  opacity: 0.3;
  transform: translateY(0px);
  transition: all 1000ms ease;
}

.main-slider__shape-2 {
  position: absolute;
  top: 0;
  right: -20px;
  border-top: 250px solid var(--tecture-base);
  border-right: 195px solid transparent;
  border-left: 215px solid transparent;
  opacity: 0;
  transform: translateY(-200px);
  z-index: 1;
}

.swiper-slide-active .main-slider__shape-2 {
  opacity: 0.2;
  transform: translateY(0px);
  transition: all 1000ms ease;
  transition-delay: 1500ms;
}

.main-slider__shape-3 {
  position: absolute;
  bottom: 0;
  left: 0;
  border-bottom: 415px solid var(--tecture-black);
  border-right: 525px solid transparent;
  border-left: 725px solid transparent;
  opacity: 0;
  transform: translateY(400px);
  z-index: 1;
}

.swiper-slide-active .main-slider__shape-3 {
  opacity: 0.5;
  transform: translateY(0px);
  transition: all 1000ms ease;
  transition-delay: 1000ms;
}

.main-slider__shape-4 {
  position: absolute;
  bottom: 0;
  right: 0;
  border-bottom: 415px solid var(--tecture-black);
  border-right: 525px solid transparent;
  border-left: 725px solid transparent;
  opacity: 0;
  transform: translateY(400px);
  z-index: 1;
}

.swiper-slide-active .main-slider__shape-4 {
  opacity: 0.4;
  transform: translateY(0px);
  transition: all 1000ms ease;
  transition-delay: 1500ms;
}

.main-slider__content {
  position: relative;
  display: block;
  text-align: center;
  z-index: 10;
}

.main-slider__video-link {
  position: relative;
  display: block;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(-120px);
  transform: translateY(-120px);
  -webkit-transition-delay: 1000ms;
  transition-delay: 1000ms;
  -webkit-transition: opacity 1500ms ease, -webkit-transform 1500ms ease;
  transition: opacity 1500ms ease, -webkit-transform 1500ms ease;
  transition: transform 1500ms ease, opacity 1500ms ease;
  transition: transform 1500ms ease, opacity 1500ms ease,
    -webkit-transform 1500ms ease;
}

.main-slider__video-icon {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 95px;
  height: 95px;
  line-height: 95px;
  text-align: center;
  font-size: 25px;
  color: var(--tecture-white);
  background-color: rgba(var(--tecture-white-rgb), 0.3);
  border-radius: 50%;
  margin: 0 auto 0;
  transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
}

.main-slider__video-icon:hover {
  background-color: var(--tecture-base);
  color: var(--tecture-white);
}

.main-slider__video-link .ripple,
.main-slider__video-icon .ripple:before,
.main-slider__video-icon .ripple:after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 95px;
  height: 95px;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -ms-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.6);
  -o-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.6);
  -webkit-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.6);
  box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
}

.main-slider__video-icon .ripple:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}

.main-slider__video-icon .ripple:after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

.main-slider__title {
  position: relative;
  display: block;
  font-size: 70px;
  color: var(--tecture-white);
  font-weight: 700;
  line-height: 90px;
  text-transform: uppercase;
  margin-top: 27px;
  margin-bottom: 10px;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(-120px);
  transform: translateY(-120px);
  /* -webkit-transition-delay: 1100ms;
   transition-delay: 1100ms; */
  -webkit-transition-delay: 800ms;
  transition-delay: 800ms;
  -webkit-transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
  transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
  transition: transform 2000ms ease, opacity 2000ms ease;
  transition: transform 2000ms ease, opacity 2000ms ease,
    -webkit-transform 2000ms ease;
}

.main-slider__btn-box {
  position: relative;
  display: block;
  margin-top: 46px;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(120px);
  transform: translateY(120px);
  -webkit-transition-delay: 1200ms;
  transition-delay: 1200ms;
  -webkit-transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
  transition: opacity 2000ms ease, -webkit-transform 2000ms ease;
  transition: transform 2000ms ease, opacity 2000ms ease;
  transition: transform 2000ms ease, opacity 2000ms ease,
    -webkit-transform 2000ms ease;
}

.main-slider__btn {
  padding: 18px 30px 18px;
}

.main-slider__btn:after {
  width: 51% !important;
}

.swiper-slide-active .main-slider__video-link {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0) translateX(0);
  transform: translateY(0) translateX(0);
}

.swiper-slide-active .main-slider__title {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0) translateX(0);
  transform: translateY(0) translateX(0);
}

.swiper-slide-active .main-slider__btn-box {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0) translateX(0);
  transform: translateY(0) translateX(0);
}

.main-slider .owl-theme .owl-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1320px;
  width: 100%;
  position: absolute;
  bottom: 61px;
  left: 0;
  right: 0;
  padding: 0 15px;
  margin: 0 auto;
  height: 0;
  line-height: 0;
}

.main-slider .owl-theme .owl-dots .owl-dot + .owl-dot {
  margin-left: 10px;
}

.main-slider .owl-theme .owl-dots .owl-dot span {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: rgba(var(--tecture-white-rgb), 0.3);
  border: 2px solid transparent;
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-slider .owl-theme .owl-dots .owl-dot:hover span,
.main-slider .owl-theme .owl-dots .owl-dot.active span {
  background-color: rgba(var(--tecture-white-rgb), 0);
  border: 2px solid var(--tecture-white);
}

.main-slider .owl-theme .owl-nav {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  max-width: 100%;
  width: 100%;
  padding: 0px 80px;
  margin: 0 auto;
  height: 0;
  line-height: 0;
  transform: translateY(-50%);
}

.main-slider .owl-theme .owl-nav [class*="owl-"] {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  z-index: 100;
  width: 60px;
  height: 60px;
  font-size: 20px !important;
  color: rgba(var(--tecture-white-rgb), 0.3) !important;
  opacity: 1;
  margin: 0;
  text-align: center;
  transition: all 500ms ease;
  border-radius: 50%;
  background-color: transparent !important;
  border: 2px solid rgba(var(--tecture-white-rgb), 0.3) !important;
}

.main-slider .owl-theme .owl-nav [class*="owl-"]:hover {
  color: rgba(var(--tecture-white-rgb), 1) !important;
  border: 2px solid rgba(var(--tecture-white-rgb), 1) !important;
}

.main-slider .owl-theme .owl-nav [class*="owl-"] + [class*="owl-"] {
  margin-left: 0px;
}

/*--------------------------------------------------------------
  # Main Slider Two
  --------------------------------------------------------------*/
.main-slider-two {
  position: relative;
  display: block;
  background-color: #0a0c09;
  padding-left: 100px;
  z-index: 10;
}

.main-slider-two .item {
  position: relative;
  padding-top: 214px;
  padding-bottom: 240px;
  background-color: var(--tecture-black);
  z-index: 10;
}

.main-slider-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: opacity 1500ms ease-in, -webkit-transform 7000ms ease;
  transition: transform 7000ms ease, opacity 1500ms ease-in;
  transition: transform 7000ms ease, opacity 1500ms ease-in,
    -webkit-transform 7000ms ease;
  z-index: 1;
}

.main-slider-two__bg:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(8, 10, 7, 0.6);
  z-index: -1;
}

.swiper-slide-active .main-slider-two__bg {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.main-slider-two__content {
  position: relative;
  display: block;
  z-index: 10;
}

.main-slider-two__title {
  position: relative;
  font-size: 70px;
  color: var(--tecture-white);
  font-weight: 700;
  line-height: 90px;
  font-style: normal;
  text-transform: uppercase;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(-200px);
}

.swiper-slide-active .main-slider-two__title {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1300ms;
}

.main-slider-two__text {
  position: relative;
  display: inline-block;
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  color: var(--tecture-white);
  margin-top: 29px;
  margin-bottom: 51px;
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(-200px);
}

.swiper-slide-active .main-slider-two__text {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1500ms;
}

.main-slider-two__btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}

.swiper-slide-active .main-slider-two__btn-box {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}

.main-slider-two .owl-nav {
  position: absolute;
  top: 50%;
  right: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  transform: translateY(-50%);
  margin: 0 !important;
  z-index: 100;
}

.main-slider-two .owl-nav .owl-next,
.main-slider-two .owl-nav .owl-prev {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  z-index: 100;
  width: 60px;
  height: 60px;
  font-size: 20px !important;
  color: rgba(var(--tecture-white-rgb), 0.3) !important;
  opacity: 1;
  margin: 0;
  text-align: center;
  transition: all 500ms ease;
  border-radius: 50%;
  background-color: transparent !important;
  border: 2px solid rgba(var(--tecture-white-rgb), 0.3) !important;
}

.main-slider-two .owl-nav .owl-prev {
  margin-bottom: 10px;
}

.main-slider-two .owl-nav .owl-next span,
.main-slider-two .owl-nav .owl-prev span {
  display: block;
  transition: all 500ms ease;
}

.main-slider-two .owl-nav .owl-next span,
.main-slider-two .owl-nav .owl-prev span,
.main-slider-two .owl-nav .owl-prev span:before,
.main-slider-two .owl-nav .owl-next span:before {
  color: rgba(var(--tecture-white-rgb), 0.3) !important;
  transition: all 500ms ease;
}

.main-slider-two .owl-nav .owl-next:hover span,
.main-slider-two .owl-nav .owl-prev:hover span,
.main-slider-two .owl-nav .owl-prev:hover span:before,
.main-slider-two .owl-nav .owl-next:hover span:before {
  color: rgba(var(--tecture-white-rgb), 1) !important;
}

.main-slider-two .owl-nav .owl-next:hover,
.main-slider-two .owl-nav .owl-prev:hover {
  border: 2px solid rgba(var(--tecture-white-rgb), 1) !important;
}

.main-slider-two .owl-theme .owl-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  max-width: 100%;
  width: 100%;
  position: absolute;
  bottom: 70px;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0 auto !important;
  height: 0;
  line-height: 0;
}

.main-slider-two .owl-theme .owl-dots .owl-dot + .owl-dot {
  margin-left: 10px;
}

.main-slider-two .owl-theme .owl-dots .owl-dot span {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: rgba(var(--tecture-white-rgb), 0.3);
  border: 2px solid transparent;
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-slider-two .owl-theme .owl-dots .owl-dot:hover span,
.main-slider-two .owl-theme .owl-dots .owl-dot.active span {
  background-color: rgba(var(--tecture-white-rgb), 0);
  border: 2px solid var(--tecture-white);
}

/*--------------------------------------------------------------
  # Main Slider Three
  --------------------------------------------------------------*/
.main-slider-three {
  position: relative;
  display: block;
  background-color: #0a0c09;
  z-index: 1;
}

.container-full {
  position: relative;
  display: block;
  z-index: 1;
}

.main-slider-three__slider {
  position: relative;
  display: block;
  z-index: 1;
}

.main-slider-three__left {
  position: relative;
  display: block;
  margin-top: 126px;
  margin-left: 100px;
  z-index: 1;
}

#main-slider-three__thumb {
  position: relative;
  display: block;
}

.main-slider-three__content-one {
  position: relative;
  display: block;
}

.main-slider-three__title-one {
  font-size: 80px;
  text-transform: uppercase;
  font-weight: 900;
  color: rgba(35, 36, 35, 0.3);
  letter-spacing: 5px;
  position: relative;
  display: block;
  margin-bottom: -25px;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(-200px);
  z-index: -1;
}

.swiper-slide-thumb-active .main-slider-three__title-one {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 500ms;
}

.main-slider-three__title-two {
  color: var(--tecture-white);
  font-weight: 700;
  text-transform: uppercase;
  font-style: normal;
  font-size: 50px;
  line-height: 60px;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(-80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}

.swiper-slide-thumb-active .main-slider-three__title-two {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1000ms;
}

.main-slider-three__price {
  font-size: 26px;
  color: var(--tecture-base);
  font-weight: 400;
  font-style: normal;
  line-height: 36px;
  margin-top: 11px;
  margin-bottom: 25px;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}

.swiper-slide-thumb-active .main-slider-three__price {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1200ms;
}

.main-slider-three__btn-box {
  position: relative;
  display: block;
  margin-bottom: -20px;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1400ms ease;
  z-index: 5;
}

.swiper-slide-thumb-active .main-slider-three__btn-box {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}

.main-slider-three__title-three {
  font-size: 80px;
  text-transform: uppercase;
  font-weight: 900;
  color: rgba(35, 36, 35, 0.3);
  letter-spacing: 10px;
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(200px);
}

.swiper-slide-thumb-active .main-slider-three__title-three {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 800ms;
}

.main-slider-three__right {
  position: relative;
  display: block;
  overflow: hidden;
  border-top-left-radius: 350px;
  border-bottom-left-radius: 350px;
  z-index: 1;
}

.main-slider-three__main-content {
  position: relative;
  display: block;
}

#main-slider-three__carousel {
  position: relative;
  display: block;
}

.main-slider-three__img-box {
  position: relative;
  display: block;
}

.main-slider-three__img {
  position: relative;
  display: block;
}

.main-slider-three__img img {
  width: 100%;
}

.main-slider-three__nav {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 50px;
  z-index: 100;
}

.main-slider-three__nav .swiper-button-next {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  color: var(--tecture-gray);
  background-color: transparent;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  border: 2px solid var(--tecture-bdr-color);
  margin: 0;
  text-align: center;
  opacity: 1;
  transition: all 500ms ease;
  z-index: 100;
}

.main-slider-three__nav .swiper-button-prev {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  color: var(--tecture-gray);
  background-color: transparent;
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
  border: 2px solid var(--tecture-bdr-color);
  margin: 0;
  text-align: center;
  opacity: 1;
  transition: all 500ms ease;
  z-index: 100;
}

.main-slider-three__nav .swiper-button-next:hover,
.main-slider-three__nav .swiper-button-prev:hover {
  color: var(--tecture-white);
  background-color: var(--tecture-base);
  border: 2px solid var(--tecture-base);
}

.main-slider-three__nav .swiper-button-next {
  margin-right: 10px;
}

.main-slider-three__nav .swiper-button-next i,
.main-slider-three__nav .swiper-button-prev i {
  position: relative;
  display: flex;
  align-items: center;
}

.main-slider-three__nav .swiper-button-next::after,
.main-slider-three__nav .swiper-button-prev::after {
  display: none;
}

.main-slider-three__contact-list {
  position: absolute;
  display: flex;
  align-items: center;
  transform: rotate(-90deg);
  left: -220px;
  bottom: 355px;
  z-index: 2;
}

.main-slider-three__contact-list li {
  position: relative;
  display: flex;
  align-items: center;
}

.main-slider-three__contact-list li + li {
  margin-left: 40px;
}

.main-slider-three__contact-list li .icon {
  position: relative;
  display: flex;
  align-items: center;
}

.main-slider-three__contact-list li .icon i {
  font-size: 15px;
  color: var(--tecture-base);
}

.main-slider-three__contact-list li .text {
  margin-left: 10px;
}

.main-slider-three__contact-list li .text p {
  font-size: 14px;
  color: var(--tecture-gray);
  font-weight: 500;
}

.main-slider-three__contact-list li .text p a {
  color: var(--tecture-gray);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-slider-three__contact-list li .text p a:hover {
  color: var(--tecture-base);
}

.main-slider-three__social {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
}

.main-slider-three__social a {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 15px;
  color: var(--tecture-gray);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-slider-three__social a:hover {
  color: var(--tecture-base);
}

/*--------------------------------------------------------------
# Main Slider Four
--------------------------------------------------------------*/
.main-slider-four {
  position: relative;
  display: block;
  padding: 0px 0px 0px;
}

.main-slider-four .item {
  position: relative;
  padding: 180px 0px 200px;
  background-color: var(--tecture-black);
  z-index: 10;
}

.main-slider-four__content {
  position: relative;
  display: block;
  overflow: hidden;
  max-width: 690px;
  width: 100%;
  z-index: 10;
}

.main-slider-four__content .title {
  position: relative;
  display: block;
  padding-bottom: 41px;
  opacity: 0;
  -webkit-transform: perspective(400px) rotateY(-40deg) translateX(-100px);
  -ms-transform: perspective(400px) rotateY(-40deg) translateX(-100px);
  transform: perspective(400px) rotateY(-40deg) translateX(-100px);
  -webkit-transform-origin: bottom;
  -ms-transform-origin: bottom;
  transform-origin: bottom;
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
  z-index: 10;
}

.swiper-slide-active .main-slider-four__content .title {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateY(0deg) translateX(0px);
  -ms-transform: perspective(400px) rotateY(0deg) translateX(0px);
  transform: perspective(400px) rotateY(0deg) translateX(0px);
  /* -webkit-transition-delay: 1500ms;
  -moz-transition-delay: 1500ms;
  -ms-transition-delay: 1500ms;
  -o-transition-delay: 1500ms;
  transition-delay: 1500ms; */
  -webkit-transition-delay: 400ms;
  -moz-transition-delay: 400ms;
  -ms-transition-delay: 400ms;
  -o-transition-delay: 400ms;
  transition-delay: 400ms;
}

.main-slider-four__content .title h2 {
  font-size: 80px;
  line-height: 1.1em;
  font-weight: 700;
  text-transform: uppercase;
}

.main-slider-four__content .title h2 span {
  color: var(--tecture-base);
  font-style: italic;
}

.main-slider-four__content .text {
  position: relative;
  display: block;
  opacity: 0;
  -webkit-transform: perspective(400px) rotateY(0deg) translateY(80px);
  -ms-transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  -webkit-transform-origin: bottom;
  -ms-transform-origin: bottom;
  transform-origin: bottom;
  -webkit-transition: all 1000ms ease;
  -moz-transition: all 1000ms ease;
  -ms-transition: all 1000ms ease;
  -o-transition: all 1000ms ease;
  transition: all 1000ms ease;
  z-index: 10;
}

.swiper-slide-active .main-slider-four__content .text {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateY(0deg) translateX(0px);
  -ms-transform: perspective(400px) rotateY(0deg) translateX(0px);
  transform: perspective(400px) rotateY(0deg) translateX(0px);
  /* -webkit-transition-delay: 2000ms;
  -moz-transition-delay: 2000ms;
  -ms-transition-delay: 2000ms;
  -o-transition-delay: 2000ms;
  transition-delay: 2000ms; */
  -webkit-transition-delay: 1200ms;
  -moz-transition-delay: 1200ms;
  -ms-transition-delay: 1200ms;
  -o-transition-delay: 1200ms;
  transition-delay: 1200ms;
}

.main-slider-four__content .text p {
  margin: 0;
}

.main-slider-four__content .btn-box {
  position: relative;
  display: block;
  line-height: 0;
  padding-top: 53px;
  opacity: 0;
  -webkit-transform: perspective(400px) rotateY(0deg) translateY(-100px);
  -ms-transform: perspective(400px) rotateY(0deg) translateY(-100px);
  transform: perspective(400px) rotateY(0deg) translateY(-100px);
  -webkit-transform-origin: bottom;
  -ms-transform-origin: bottom;
  transform-origin: bottom;
  -webkit-transition: all 3000ms ease;
  -moz-transition: all 3000ms ease;
  -ms-transition: all 3000ms ease;
  -o-transition: all 3000ms ease;
  transition: all 3000ms ease;
  z-index: 10;
}

.swiper-slide-active .main-slider-four__content .btn-box {
  visibility: visible;
  opacity: 1;
  -webkit-transform: perspective(400px) rotateY(0deg) translateY(0px);
  -ms-transform: perspective(400px) rotateY(0deg) translateY(0px);
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  -webkit-transition-delay: 1000ms;
  -moz-transition-delay: 1000ms;
  -ms-transition-delay: 1000ms;
  -o-transition-delay: 1000ms;
  transition-delay: 1000ms;
}

/*--------------------------------------------------------------
  # End
  --------------------------------------------------------------*/
